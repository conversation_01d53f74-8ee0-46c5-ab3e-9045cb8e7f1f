# 🚀 Multi-Platform Deployment Script (PowerShell)
# Based on official Astro deployment documentation: https://docs.astro.build/en/guides/deploy/
# This script deploys the Astro site to multiple platforms following official guidelines

param(
    [switch]$SkipBuild = $false,
    [switch]$GitHubPagesOnly = $false,
    [string]$Platform = "all"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"
$Magenta = "Magenta"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Write-Platform {
    param([string]$Message)
    Write-Host $Message -ForegroundColor $Cyan
}

# Check if required tools are installed
function Test-Dependencies {
    Write-Status "Checking dependencies..."

    # Check for pnpm
    if (-not (Get-Command pnpm -ErrorAction SilentlyContinue)) {
        Write-Error "pnpm is not installed. Please install it first: npm install -g pnpm"
        exit 1
    }

    # Check for git
    if (-not (Get-Command git -ErrorAction SilentlyContinue)) {
        Write-Error "git is not installed. Please install Git first."
        exit 1
    }

    # Check if we're in a git repository
    if (-not (Test-Path ".git")) {
        Write-Error "Not in a git repository. Please run 'git init' first."
        exit 1
    }

    Write-Success "All dependencies are installed"
}

# Build the site following Astro official guidelines
function Build-Site {
    if ($SkipBuild) {
        Write-Warning "Skipping build step"
        return
    }

    Write-Status "Installing dependencies..."
    try {
        pnpm install --frozen-lockfile
        Write-Success "Dependencies installed"
    }
    catch {
        Write-Error "Failed to install dependencies: $_"
        exit 1
    }

    Write-Status "Building the site for production..."
    try {
        pnpm build
        Write-Success "Site built successfully"

        # Verify dist directory exists
        if (-not (Test-Path "dist")) {
            Write-Error "Build failed: dist directory not found"
            exit 1
        }

        Write-Status "Build output size:"
        $size = (Get-ChildItem -Recurse "dist" | Measure-Object -Property Length -Sum).Sum / 1MB
        Write-Host "  Total: $([math]::Round($size, 2)) MB" -ForegroundColor $Cyan
    }
    catch {
        Write-Error "Build failed: $_"
        exit 1
    }
}

# 1. Deploy to GitHub Pages (Manual Method)
# Reference: https://docs.astro.build/en/guides/deploy/github/
function Deploy-GitHubPages {
    Write-Platform "🚀 Deploying to GitHub Pages..."

    Write-Status "Using manual deployment method (no GitHub Actions needed)..."

    try {
        # Method 1: Try git subtree (recommended for GitHub Pages)
        Write-Status "Attempting git subtree deployment..."

        # Check if we have uncommitted changes
        $gitStatus = git status --porcelain
        if ($gitStatus) {
            Write-Status "Committing current changes..."
            git add .
            git commit -m "Update before deployment"
        }

        # Check if gh-pages branch exists remotely
        $remoteBranches = git branch -r
        if ($remoteBranches -match "origin/gh-pages") {
            Write-Status "Updating existing gh-pages branch..."
            git subtree push --prefix dist origin gh-pages
        } else {
            Write-Status "Creating new gh-pages branch..."
            git subtree push --prefix dist origin gh-pages
        }

        Write-Success "✅ Deployed to GitHub Pages!"
        Write-Host "  📄 Your site will be available at: https://hugetiny.github.io/broke-ass-plan/" -ForegroundColor $Cyan
        Write-Host "  ⚡ Note: It may take a few minutes for changes to appear" -ForegroundColor $Yellow
        Write-Host "  🔧 Make sure to enable GitHub Pages in repository settings" -ForegroundColor $Yellow
    }
    catch {
        Write-Warning "Git subtree failed, trying manual branch method..."

        try {
            # Method 2: Manual gh-pages branch creation
            $currentBranch = git branch --show-current
            $tempDir = "temp-gh-pages"

            # Clean up any existing temp directory
            if (Test-Path $tempDir) {
                Remove-Item -Recurse -Force $tempDir
            }

            # Create temporary copy of dist
            Copy-Item -Recurse "dist" $tempDir

            # Create or checkout gh-pages branch
            git show-ref --verify --quiet refs/heads/gh-pages
            if ($LASTEXITCODE -eq 0) {
                Write-Status "Switching to existing gh-pages branch..."
                git checkout gh-pages
            } else {
                Write-Status "Creating new orphan gh-pages branch..."
                git checkout --orphan gh-pages
            }

            # Clear existing content (except .git)
            Get-ChildItem -Force | Where-Object { $_.Name -ne ".git" -and $_.Name -ne $tempDir } | Remove-Item -Recurse -Force

            # Copy new content
            Copy-Item -Recurse "$tempDir/*" . -Force

            # Add and commit
            git add .
            git commit -m "Deploy to GitHub Pages - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

            # Push to remote
            git push -u origin gh-pages -f

            # Return to original branch
            git checkout $currentBranch

            # Clean up
            Remove-Item -Recurse -Force $tempDir

            Write-Success "✅ Deployed to GitHub Pages using manual method!"
            Write-Host "  📄 Your site will be available at: https://hugetiny.github.io/broke-ass-plan/" -ForegroundColor $Cyan
        }
        catch {
            Write-Error "All GitHub Pages deployment methods failed: $_"
            Write-Host "Manual steps:" -ForegroundColor $Yellow
            Write-Host "  1. Go to GitHub repository settings" -ForegroundColor $Cyan
            Write-Host "  2. Enable GitHub Pages" -ForegroundColor $Cyan
            Write-Host "  3. Select 'Deploy from a branch'" -ForegroundColor $Cyan
            Write-Host "  4. Choose 'gh-pages' branch" -ForegroundColor $Cyan
        }
    }
}

# 2. Deploy to Netlify
# Reference: https://docs.astro.build/en/guides/deploy/netlify/
function Deploy-Netlify {
    Write-Platform "🌐 Deploying to Netlify..."

    if (Get-Command netlify -ErrorAction SilentlyContinue) {
        try {
            # Check if netlify.toml exists
            if (-not (Test-Path "netlify.toml")) {
                Write-Warning "netlify.toml not found. Deployment may not work correctly."
            }

            Write-Status "Deploying to Netlify..."
            netlify deploy --prod --dir=dist
            Write-Success "✅ Deployed to Netlify!"
            Write-Host "  🌐 Check your Netlify dashboard for the live URL" -ForegroundColor $Cyan
        }
        catch {
            Write-Error "Netlify deployment failed: $_"
            Write-Status "Make sure you're logged in: netlify login"
        }
    } else {
        Write-Warning "Netlify CLI not found. Install with: npm install -g netlify-cli"
        Write-Status "Alternative: Connect your GitHub repo at https://app.netlify.com/"
    }
}

# 3. Deploy to Vercel
# Reference: https://docs.astro.build/en/guides/deploy/vercel/
function Deploy-Vercel {
    Write-Platform "▲ Deploying to Vercel..."

    if (Get-Command vercel -ErrorAction SilentlyContinue) {
        try {
            # Check if vercel.json exists
            if (-not (Test-Path "vercel.json")) {
                Write-Warning "vercel.json not found. Using default settings."
            }

            Write-Status "Deploying to Vercel..."
            vercel --prod
            Write-Success "✅ Deployed to Vercel!"
            Write-Host "  ▲ Check your Vercel dashboard for the live URL" -ForegroundColor $Cyan
        }
        catch {
            Write-Error "Vercel deployment failed: $_"
            Write-Status "Make sure you're logged in: vercel login"
        }
    } else {
        Write-Warning "Vercel CLI not found. Install with: npm install -g vercel"
        Write-Status "Alternative: Connect your GitHub repo at https://vercel.com/"
    }
}

# 4. Deploy to Cloudflare Pages
# Reference: https://docs.astro.build/en/guides/deploy/cloudflare/
function Deploy-Cloudflare {
    Write-Platform "☁️ Deploying to Cloudflare Pages..."

    if (Get-Command wrangler -ErrorAction SilentlyContinue) {
        try {
            Write-Status "Deploying to Cloudflare Pages..."
            wrangler pages deploy dist --project-name broke-ass-plan
            Write-Success "✅ Deployed to Cloudflare Pages!"
            Write-Host "  ☁️ Your site will be available at: https://broke-ass-plan.pages.dev/" -ForegroundColor $Cyan
        }
        catch {
            Write-Error "Cloudflare deployment failed: $_"
            Write-Status "Make sure you're logged in: wrangler login"
        }
    } else {
        Write-Warning "Wrangler CLI not found. Install with: npm install -g wrangler"
        Write-Status "Alternative: Connect your GitHub repo at https://dash.cloudflare.com/pages"
    }
}

# 5. Deploy to Firebase Hosting
# Reference: https://docs.astro.build/en/guides/deploy/google-firebase/
function Deploy-Firebase {
    Write-Platform "🔥 Deploying to Firebase Hosting..."

    if (Get-Command firebase -ErrorAction SilentlyContinue) {
        try {
            # Check if firebase.json exists
            if (-not (Test-Path "firebase.json")) {
                Write-Warning "firebase.json not found. Creating basic configuration..."

                $firebaseConfig = @{
                    hosting = @{
                        public = "dist"
                        ignore = @("firebase.json", "**/.*", "**/node_modules/**")
                        rewrites = @(@{
                            source = "**"
                            destination = "/index.html"
                        })
                    }
                } | ConvertTo-Json -Depth 10

                Set-Content -Path "firebase.json" -Value $firebaseConfig -Encoding UTF8
                Write-Success "firebase.json created"
            }

            Write-Status "Deploying to Firebase Hosting..."
            firebase deploy --only hosting
            Write-Success "✅ Deployed to Firebase Hosting!"
            Write-Host "  🔥 Check your Firebase console for the live URL" -ForegroundColor $Cyan
        }
        catch {
            Write-Error "Firebase deployment failed: $_"
            Write-Status "Make sure you're logged in: firebase login"
            Write-Status "Initialize project: firebase init hosting"
        }
    } else {
        Write-Warning "Firebase CLI not found. Install with: npm install -g firebase-tools"
        Write-Status "Alternative: Use Firebase console at https://console.firebase.google.com/"
    }
}

# 6. Deploy to Surge.sh
# Reference: https://docs.astro.build/en/guides/deploy/surge/
function Deploy-Surge {
    Write-Platform "🌊 Deploying to Surge.sh..."

    if (Get-Command surge -ErrorAction SilentlyContinue) {
        try {
            Write-Status "Deploying to Surge.sh..."
            surge dist/ broke-ass-plan.surge.sh
            Write-Success "✅ Deployed to Surge.sh!"
            Write-Host "  🌊 Your site is live at: https://broke-ass-plan.surge.sh/" -ForegroundColor $Cyan
        }
        catch {
            Write-Error "Surge deployment failed: $_"
            Write-Status "Make sure you're logged in: surge login"
        }
    } else {
        Write-Warning "Surge CLI not found. Install with: npm install -g surge"
    }
}

# 7. Deploy to Render
# Reference: https://docs.astro.build/en/guides/deploy/render/
function Deploy-Render {
    Write-Platform "🎨 Deploying to Render..."
    Write-Status "Render deployment requires connecting your GitHub repository"
    Write-Host "  1. Go to https://dashboard.render.com/" -ForegroundColor $Cyan
    Write-Host "  2. Click 'New Static Site'" -ForegroundColor $Cyan
    Write-Host "  3. Connect your GitHub repository: hugetiny/broke-ass-plan" -ForegroundColor $Cyan
    Write-Host "  4. Use these settings:" -ForegroundColor $Cyan
    Write-Host "     - Build Command: pnpm build" -ForegroundColor $Yellow
    Write-Host "     - Publish Directory: dist" -ForegroundColor $Yellow
    Write-Success "Manual setup required for Render"
}

# 8. Deploy to Deno Deploy
# Reference: https://docs.astro.build/en/guides/deploy/deno/
function Deploy-Deno {
    Write-Platform "🦕 Deploying to Deno Deploy..."

    if (Get-Command deployctl -ErrorAction SilentlyContinue) {
        try {
            Write-Status "Deploying to Deno Deploy..."
            deployctl deploy --project=broke-ass-plan --include=dist dist/index.html
            Write-Success "✅ Deployed to Deno Deploy!"
            Write-Host "  🦕 Check your Deno Deploy dashboard for the live URL" -ForegroundColor $Cyan
        }
        catch {
            Write-Error "Deno Deploy deployment failed: $_"
        }
    } else {
        Write-Warning "Deno Deploy CLI not found. Install Deno first: https://deno.land/"
        Write-Status "Alternative: Connect your GitHub repo at https://dash.deno.com/"
    }
}

# Show available platforms
function Show-AvailablePlatforms {
    Write-Host "`n🌐 Available Deployment Platforms:" -ForegroundColor $Cyan
    Write-Host "  1. github     - GitHub Pages (Free, Git-based)" -ForegroundColor $Green
    Write-Host "  2. netlify    - Netlify (CDN, Forms, Functions)" -ForegroundColor $Green
    Write-Host "  3. vercel     - Vercel (Edge Functions, Analytics)" -ForegroundColor $Green
    Write-Host "  4. cloudflare - Cloudflare Pages (Global CDN)" -ForegroundColor $Green
    Write-Host "  5. firebase   - Firebase Hosting (Google Cloud)" -ForegroundColor $Green
    Write-Host "  6. surge      - Surge.sh (Simple, Fast)" -ForegroundColor $Green
    Write-Host "  7. render     - Render (Auto-deploy)" -ForegroundColor $Yellow
    Write-Host "  8. deno       - Deno Deploy (Edge runtime)" -ForegroundColor $Yellow
    Write-Host "`n💡 Usage examples:" -ForegroundColor $Blue
    Write-Host "  .\scripts\deploy-all.ps1                    # Deploy to all platforms" -ForegroundColor $Cyan
    Write-Host "  .\scripts\deploy-all.ps1 -Platform github  # Deploy to GitHub Pages only" -ForegroundColor $Cyan
    Write-Host "  .\scripts\deploy-all.ps1 -GitHubPagesOnly  # Deploy to GitHub Pages only" -ForegroundColor $Cyan
    Write-Host "  .\scripts\deploy-all.ps1 -SkipBuild        # Skip build step" -ForegroundColor $Cyan
}

# Main deployment function
function Start-Deployment {
    Write-Host "🚀 Multi-Platform Deployment Script" -ForegroundColor $Magenta
    Write-Host "Based on official Astro documentation: https://docs.astro.build/en/guides/deploy/" -ForegroundColor $Blue
    Write-Host ""

    # Show help if requested
    if ($Platform -eq "help" -or $Platform -eq "list") {
        Show-AvailablePlatforms
        return
    }

    Test-Dependencies
    Build-Site

    # Deploy based on parameters
    if ($GitHubPagesOnly -or $Platform -eq "github") {
        Deploy-GitHubPages
    }
    elseif ($Platform -eq "netlify") {
        Deploy-Netlify
    }
    elseif ($Platform -eq "vercel") {
        Deploy-Vercel
    }
    elseif ($Platform -eq "cloudflare") {
        Deploy-Cloudflare
    }
    elseif ($Platform -eq "firebase") {
        Deploy-Firebase
    }
    elseif ($Platform -eq "surge") {
        Deploy-Surge
    }
    elseif ($Platform -eq "render") {
        Deploy-Render
    }
    elseif ($Platform -eq "deno") {
        Deploy-Deno
    }
    elseif ($Platform -eq "all") {
        Write-Status "Deploying to all available platforms..."
        Deploy-GitHubPages
        Deploy-Netlify
        Deploy-Vercel
        Deploy-Cloudflare
        Deploy-Firebase
        Deploy-Surge
        Deploy-Render
        Deploy-Deno
    }
    else {
        Write-Error "Unknown platform: $Platform"
        Show-AvailablePlatforms
        return
    }

    Write-Host "`n🎉 Deployment completed!" -ForegroundColor $Green
    Write-Host "📊 Deployment Summary:" -ForegroundColor $Blue
    Write-Host "  • GitHub Pages: https://hugetiny.github.io/broke-ass-plan/" -ForegroundColor $Cyan
    Write-Host "  • Netlify: Check your dashboard" -ForegroundColor $Cyan
    Write-Host "  • Vercel: Check your dashboard" -ForegroundColor $Cyan
    Write-Host "  • Cloudflare: https://broke-ass-plan.pages.dev/" -ForegroundColor $Cyan
    Write-Host "  • Firebase: Check your console" -ForegroundColor $Cyan
    Write-Host "  • Surge.sh: https://broke-ass-plan.surge.sh/" -ForegroundColor $Cyan
    Write-Host "`n💡 Note: Some platforms may take a few minutes to update" -ForegroundColor $Yellow
}

# Run the main function
Start-Deployment
