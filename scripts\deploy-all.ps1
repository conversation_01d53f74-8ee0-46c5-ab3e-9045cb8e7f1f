# 🚀 Multi-Platform Deployment Script (PowerShell)
# This script deploys the Astro site to multiple platforms

param(
    [switch]$SkipBuild = $false
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

# Check if required tools are installed
function Test-Dependencies {
    Write-Status "Checking dependencies..."
    
    # Check for pnpm
    if (-not (Get-Command pnpm -ErrorAction SilentlyContinue)) {
        Write-Error "pnpm is not installed. Please install it first."
        exit 1
    }
    
    # Check for git
    if (-not (Get-Command git -ErrorAction SilentlyContinue)) {
        Write-Error "git is not installed. Please install it first."
        exit 1
    }
    
    Write-Success "All dependencies are installed"
}

# Build the site
function Build-Site {
    if ($SkipBuild) {
        Write-Warning "Skipping build step"
        return
    }
    
    Write-Status "Building the site..."
    pnpm install
    pnpm build
    Write-Success "Site built successfully"
}

# Deploy to Netlify
function Deploy-Netlify {
    if (Get-Command netlify -ErrorAction SilentlyContinue) {
        Write-Status "Deploying to Netlify..."
        netlify deploy --prod --dir=dist
        Write-Success "Deployed to Netlify"
    } else {
        Write-Warning "Netlify CLI not found, skipping Netlify deployment"
    }
}

# Deploy to Vercel
function Deploy-Vercel {
    if (Get-Command vercel -ErrorAction SilentlyContinue) {
        Write-Status "Deploying to Vercel..."
        vercel --prod
        Write-Success "Deployed to Vercel"
    } else {
        Write-Warning "Vercel CLI not found, skipping Vercel deployment"
    }
}

# Deploy to Firebase
function Deploy-Firebase {
    if (Get-Command firebase -ErrorAction SilentlyContinue) {
        Write-Status "Deploying to Firebase..."
        firebase deploy
        Write-Success "Deployed to Firebase"
    } else {
        Write-Warning "Firebase CLI not found, skipping Firebase deployment"
    }
}

# Deploy to Surge.sh
function Deploy-Surge {
    if (Get-Command surge -ErrorAction SilentlyContinue) {
        Write-Status "Deploying to Surge.sh..."
        surge dist/ broke-ass-plan.surge.sh
        Write-Success "Deployed to Surge.sh"
    } else {
        Write-Warning "Surge CLI not found, skipping Surge deployment"
    }
}

# Deploy to GitHub Pages
function Deploy-GitHubPages {
    Write-Status "Preparing GitHub Pages deployment..."
    
    # Create a temporary directory for gh-pages
    if (Test-Path "gh-pages-temp") {
        Remove-Item -Recurse -Force "gh-pages-temp"
    }
    
    New-Item -ItemType Directory -Name "gh-pages-temp"
    Copy-Item -Recurse "dist/*" "gh-pages-temp/"
    
    Set-Location "gh-pages-temp"
    git init
    git add .
    git commit -m "Deploy to GitHub Pages"
    git branch -M gh-pages
    git remote <NAME_EMAIL>:hugetiny/broke-ass-plan.git
    git push -f origin gh-pages
    
    Set-Location ..
    Remove-Item -Recurse -Force "gh-pages-temp"
    
    Write-Success "Deployed to GitHub Pages"
}

# Main deployment function
function Start-Deployment {
    Write-Status "🚀 Multi-Platform Deployment Started"
    
    Test-Dependencies
    Build-Site
    
    # Deploy to various platforms
    Deploy-Netlify
    Deploy-Vercel
    Deploy-Firebase
    Deploy-Surge
    Deploy-GitHubPages
    
    Write-Success "🎉 Multi-platform deployment completed!"
    Write-Status "Your site is now available on multiple platforms:"
    Write-Host "  • GitHub Pages: https://hugetiny.github.io/broke-ass-plan/" -ForegroundColor Cyan
    Write-Host "  • Netlify: https://broke-ass-plan.netlify.app/" -ForegroundColor Cyan
    Write-Host "  • Vercel: https://broke-ass-plan.vercel.app/" -ForegroundColor Cyan
    Write-Host "  • Firebase: https://broke-ass-plan.web.app/" -ForegroundColor Cyan
    Write-Host "  • Surge.sh: https://broke-ass-plan.surge.sh/" -ForegroundColor Cyan
}

# Run the main function
Start-Deployment
