#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/StudioProjects/broke-ass-plan/node_modules/.pnpm/astro@5.11.0_@types+node@24_dd732e74d9dabc8cc686346dcfe6af3f/node_modules/astro/node_modules:/mnt/c/Users/<USER>/StudioProjects/broke-ass-plan/node_modules/.pnpm/astro@5.11.0_@types+node@24_dd732e74d9dabc8cc686346dcfe6af3f/node_modules:/mnt/c/Users/<USER>/StudioProjects/broke-ass-plan/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/StudioProjects/broke-ass-plan/node_modules/.pnpm/astro@5.11.0_@types+node@24_dd732e74d9dabc8cc686346dcfe6af3f/node_modules/astro/node_modules:/mnt/c/Users/<USER>/StudioProjects/broke-ass-plan/node_modules/.pnpm/astro@5.11.0_@types+node@24_dd732e74d9dabc8cc686346dcfe6af3f/node_modules:/mnt/c/Users/<USER>/StudioProjects/broke-ass-plan/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../astro/astro.js" "$@"
else
  exec node  "$basedir/../astro/astro.js" "$@"
fi
