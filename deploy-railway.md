# 🚂 使用 Railway 部署 Astro 应用

Railway 官方支持 Astro，提供简单的 CLI 部署方式。

## 📦 安装 Railway CLI

```bash
# 使用 npm 安装
npm install -g @railway/cli

# 或使用 pnpm
pnpm add -g @railway/cli
```

## 🔐 登录 Railway

```bash
railway login
```

## 🚀 快速部署

### 方法 1: CLI 部署 (推荐)
```bash
# 在项目根目录初始化
railway init

# 部署应用
railway up
```

### 方法 2: 从模板部署
```bash
# 使用 Astro 模板
railway new --template astro
```

### 方法 3: GitHub 集成
1. 推送代码到 GitHub
2. 访问 [Railway](https://railway.com/new)
3. 选择 "Deploy from GitHub repo"
4. 选择您的仓库
5. 自动检测并部署

## 🌐 生成公共 URL

部署后需要生成公共访问地址：

```bash
# 生成域名
railway domain
```

或在 Railway Dashboard 中：
1. 进入项目设置
2. 点击 "Networking" 标签
3. 点击 "Generate Domain"

## 🔧 配置环境变量

```bash
# 设置环境变量
railway variables set NODE_VERSION=18
railway variables set ASTRO_TELEMETRY_DISABLED=1
```

## 📊 监控和日志

```bash
# 查看实时日志
railway logs

# 查看服务状态
railway status
```

## 💰 定价
- **免费套餐**: 每月 $5 免费额度
- **按使用付费**: CPU 和内存使用量计费
- **无需信用卡**: 免费套餐无需绑卡

## 🔧 高级配置

创建 `railway.toml`:
```toml
[build]
builder = "nixpacks"

[deploy]
healthcheckPath = "/"
healthcheckTimeout = 100
restartPolicyType = "on_failure"
```
