---
title: 免费云资源大全
description: 整理归纳各大云厂商免费套餐信息，帮助开发者和创业者节省成本
template: splash
hero:
  tagline: 发现并利用各大云厂商的免费资源，让你的项目零成本起步！
  image:
    file: ../../../assets/houston.webp
  actions:
    - text: 开始探索
      link: /zh-CN/guides/getting-started/
      icon: right-arrow
      variant: primary
    - text: 查看云服务商
      link: /zh-CN/providers/aws/
      icon: external
---

import { Card, CardGrid } from '@astrojs/starlight/components';

## 为什么选择免费云资源？

<CardGrid stagger>
	<Card title="零成本起步" icon="rocket">
		利用各大云厂商的免费套餐，让你的项目和想法快速验证，无需前期投入。
	</Card>
	<Card title="学习最佳实践" icon="pencil">
		通过实际使用云服务，学习现代应用架构和最佳实践。
	</Card>
	<Card title="扩展性强" icon="setting">
		当项目成长时，可以无缝升级到付费服务，享受更多资源。
	</Card>
	<Card title="多样化选择" icon="open-book">
		涵盖计算、存储、数据库、AI等各类服务，满足不同需求。
	</Card>
</CardGrid>

## 主要云服务商

我们整理了以下主要云服务商的免费套餐信息：

- **[AWS 免费套餐](/zh-CN/providers/aws/)** - 亚马逊云服务，提供12个月免费套餐
- **[Azure 免费套餐](/zh-CN/providers/azure/)** - 微软云服务，200美元免费额度
- **[Google Cloud 免费套餐](/zh-CN/providers/gcp/)** - 谷歌云，300美元免费试用
- **[阿里云免费套餐](/zh-CN/providers/aliyun/)** - 国内领先云服务商
- **[腾讯云免费套餐](/zh-CN/providers/tencent/)** - 腾讯云服务

## 服务分类

按服务类型查找适合你的免费资源：

- **[计算服务](/zh-CN/categories/compute/)** - 虚拟机、容器、无服务器
- **[存储服务](/zh-CN/categories/storage/)** - 对象存储、文件存储、备份
- **[数据库服务](/zh-CN/categories/database/)** - 关系型、NoSQL、缓存
- **[网络服务](/zh-CN/categories/network/)** - CDN、负载均衡、DNS
- **[开发工具](/zh-CN/categories/devtools/)** - CI/CD、监控、日志

## 开始使用

1. 📖 阅读我们的[入门指南](/zh-CN/guides/getting-started/)
2. 🔍 了解[如何选择合适的云服务](/zh-CN/guides/how-to-choose/)
3. 🚀 开始使用免费资源构建你的项目

---

> **提示**: 本项目基于开源社区贡献，信息会持续更新。如果你发现有用的免费资源或信息有误，欢迎贡献！
