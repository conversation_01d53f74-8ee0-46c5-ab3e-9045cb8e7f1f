{"buildCommand": "pnpm build", "outputDirectory": "dist", "devCommand": "pnpm dev", "installCommand": "pnpm install", "framework": "astro", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}