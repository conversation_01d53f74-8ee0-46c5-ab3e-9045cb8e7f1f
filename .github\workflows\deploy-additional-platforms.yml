name: 🌍 Additional Platforms Deployment

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  build:
    name: 🏗️ Build Site
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: 📦 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🏗️ Build site
        run: pnpm build

      - name: 📤 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist-additional
          path: dist/
          retention-days: 1

  # 7. Render Deployment
  deploy-render:
    name: 🎨 Deploy to Render
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Render
        uses: johnbeynon/render-deploy-action@v0.0.8
        with:
          service-id: ${{ secrets.RENDER_SERVICE_ID }}
          api-key: ${{ secrets.RENDER_API_KEY }}

  # 8. Heroku Deployment
  deploy-heroku:
    name: 💜 Deploy to Heroku
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Heroku
        uses: akhileshns/heroku-deploy@v3.13.15
        with:
          heroku_api_key: ${{ secrets.HEROKU_API_KEY }}
          heroku_app_name: ${{ secrets.HEROKU_APP_NAME }}
          heroku_email: ${{ secrets.HEROKU_EMAIL }}
          buildpack: https://github.com/heroku/heroku-buildpack-static.git

  # 9. Azure Static Web Apps
  deploy-azure:
    name: 🔷 Deploy to Azure
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist-additional
          path: dist/

      - name: 🚀 Deploy to Azure Static Web Apps
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          action: "upload"
          app_location: "/"
          output_location: "dist"

  # 10. AWS S3 + CloudFront
  deploy-aws:
    name: ☁️ Deploy to AWS
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist-additional
          path: dist/

      - name: 🔧 Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: 🚀 Deploy to S3
        run: |
          aws s3 sync dist/ s3://${{ secrets.AWS_S3_BUCKET }} --delete
          aws cloudfront create-invalidation --distribution-id ${{ secrets.AWS_CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"

  # 11. Google Cloud Storage
  deploy-gcp:
    name: 🌐 Deploy to Google Cloud
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist-additional
          path: dist/

      - name: 🔧 Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          service_account_key: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}

      - name: 🚀 Deploy to Google Cloud Storage
        run: |
          gsutil -m rsync -r -d dist/ gs://${{ secrets.GCP_BUCKET_NAME }}

  # 12. Fly.io Deployment
  deploy-flyio:
    name: 🪰 Deploy to Fly.io
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Fly.io
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: 🚀 Deploy
        run: flyctl deploy --remote-only
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  # 13. Deno Deploy
  deploy-deno:
    name: 🦕 Deploy to Deno Deploy
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist-additional
          path: dist/

      - name: 🚀 Deploy to Deno Deploy
        uses: denoland/deployctl@v1
        with:
          project: broke-ass-plan
          entrypoint: https://deno.land/std@0.140.0/http/file_server.ts
          root: dist
        env:
          DENO_DEPLOY_TOKEN: ${{ secrets.DENO_DEPLOY_TOKEN }}

  # 14. Stormkit Deployment
  deploy-stormkit:
    name: ⛈️ Deploy to Stormkit
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🚀 Deploy to Stormkit
        run: |
          curl -X POST \
            -H "Authorization: Bearer ${{ secrets.STORMKIT_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d '{"branch": "main"}' \
            https://api.stormkit.io/v1/apps/${{ secrets.STORMKIT_APP_ID }}/deployments

  # 15. Fleek Deployment
  deploy-fleek:
    name: ⚡ Deploy to Fleek
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 📥 Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist-additional
          path: dist/

      - name: 🚀 Deploy to Fleek
        uses: fleekhq/action-deploy@v1
        with:
          apiKey: ${{ secrets.FLEEK_API_KEY }}
          workDir: dist/
