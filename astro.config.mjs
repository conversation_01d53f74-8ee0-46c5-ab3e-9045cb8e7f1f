// @ts-check
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

// https://astro.build/config
export default defineConfig({
	integrations: [
		starlight({
			title: 'Multi-Cloud Free Solutions Platform',
			defaultLocale: 'root',
			locales: {
				root: {
					label: 'English',
					lang: 'en',
				},
				'zh-CN': {
					label: '简体中文',
					lang: 'zh-CN',
				},
			},
			social: [
				{
					icon: 'github',
					label: 'GitHub',
					href: 'https://github.com/hugetiny/broke-ass-plan'
				}
			],
			sidebar: [
				{
					label: '指南',
					autogenerate: { directory: 'guides' },
				},
				{
					label: '云服务商',
					autogenerate: { directory: 'providers' },
				},
				{
					label: '服务分类',
					autogenerate: { directory: 'categories' },
				},
				{
					label: '参考资料',
					autogenerate: { directory: 'reference' },
				},
			],
		}),
	],
});
