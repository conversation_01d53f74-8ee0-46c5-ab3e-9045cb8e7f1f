// @ts-check
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

// https://astro.build/config
export default defineConfig({
	integrations: [
		starlight({
			title: {
				en: 'broke ass plan',
				'zh-CN': '多云免费方案整合平台'
			},
			defaultLocale: 'en',
			locales: {
				'zh-CN': {
					label: '简体中文',
					lang: 'zh-CN',
				},
				en: {
					label: 'English',
					lang: 'en',
				},
			},
			social: [
				{
					icon: 'github',
					label: 'GitHub',
					href: 'https://github.com/hugetiny/broke-ass-plan'
				}
			],
			sidebar: [
				{
					label: 'Guides',
					autogenerate: { directory: 'guides' },
				},
				{
					label: 'Cloud Providers',
					autogenerate: { directory: 'providers' },
				},
				{
					label: 'Service Categories',
					autogenerate: { directory: 'categories' },
				},
				{
					label: 'Templates',
					autogenerate: { directory: 'templates' },
				},
			],
		}),
	],
});
