// @ts-check
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

// https://astro.build/config
export default defineConfig({
	integrations: [
		starlight({
			title: '免费云资源大全',
			defaultLocale: 'zh-CN',
			locales: {
				'zh-CN': {
					label: '简体中文',
					lang: 'zh-CN',
				},
				en: {
					label: 'English',
					lang: 'en',
				},
			},
			social: [
				{
					icon: 'github',
					label: 'GitHub',
					href: 'https://github.com/hugetiny/broke-ass-plan'
				}
			],
			sidebar: [
				{
					label: '指南',
					items: [
						{ label: '开始使用', slug: 'zh-CN/guides/getting-started' },
						{ label: '如何选择云服务', slug: 'zh-CN/guides/how-to-choose' },
					],
				},
				{
					label: '云服务商',
					items: [
						{ label: 'AWS 免费套餐', slug: 'zh-CN/providers/aws' },
						{ label: 'Azure 免费套餐', slug: 'zh-CN/providers/azure' },
						{ label: 'Google Cloud 免费套餐', slug: 'zh-CN/providers/gcp' },
						{ label: '阿里云免费套餐', slug: 'zh-CN/providers/aliyun' },
						{ label: '腾讯云免费套餐', slug: 'zh-CN/providers/tencent' },
					],
				},
				{
					label: '服务分类',
					items: [
						{ label: '计算服务', slug: 'zh-CN/categories/compute' },
						{ label: '存储服务', slug: 'zh-CN/categories/storage' },
						{ label: '数据库服务', slug: 'zh-CN/categories/database' },
						{ label: '网络服务', slug: 'zh-CN/categories/network' },
						{ label: '开发工具', slug: 'zh-CN/categories/devtools' },
					],
				},
				{
					label: '参考资料',
					autogenerate: { directory: 'zh-CN/reference' },
				},
			],
		}),
	],
});
