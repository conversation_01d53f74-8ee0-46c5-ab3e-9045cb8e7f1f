[build]
  publish = "dist"
  command = "pnpm build"

[build.environment]
  NODE_VERSION = "18"
  PNPM_VERSION = "8"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production.environment]
  ASTRO_TELEMETRY_DISABLED = "1"

[context.deploy-preview.environment]
  ASTRO_TELEMETRY_DISABLED = "1"

[context.branch-deploy.environment]
  ASTRO_TELEMETRY_DISABLED = "1"

# Headers for security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"
