# 🚀 Multi-Platform Deployment Guide

This project supports deployment to 26+ platforms as recommended by the [Astro official documentation](https://docs.astro.build/en/guides/deploy/).

## 🎯 Quick Start

### Option 1: Automated GitHub Actions (Recommended)

1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Configure Secrets**: Add the following secrets to your GitHub repository settings:

   **Netlify**:
   - `NETLIFY_SITE_ID`
   - `NETLIFY_AUTH_TOKEN`

   **Vercel**:
   - `VERCEL_TOKEN`
   - `VERCEL_ORG_ID`
   - `VERCEL_PROJECT_ID`

   **Cloudflare**:
   - `CLOUDFLARE_API_TOKEN`
   - `CLOUDFLARE_ACCOUNT_ID`

   **Firebase**:
   - `FIREBASE_SERVICE_ACCOUNT`
   - `FIREBASE_PROJECT_ID`

   **AWS**:
   - `AWS_ACCESS_KEY_ID`
   - `AWS_SECRET_ACCESS_KEY`
   - `AWS_S3_BUCKET`
   - `AWS_CLOUDFRONT_DISTRIBUTION_ID`

   **And more...**

### Option 2: One-Click CLI Deployment

1. **Make script executable**:
   ```bash
   chmod +x scripts/deploy-all.sh
   ```

2. **Install CLI tools** (optional, script will skip unavailable ones):
   ```bash
   # Netlify
   npm install -g netlify-cli
   
   # Vercel
   npm install -g vercel
   
   # Firebase
   npm install -g firebase-tools
   
   # Surge.sh
   npm install -g surge
   ```

3. **Run deployment**:
   ```bash
   ./scripts/deploy-all.sh
   ```

## 🌐 Supported Platforms

### Tier 1: Primary Platforms (Auto-deployed via GitHub Actions)

| Platform | Status | URL Pattern | Features |
|----------|--------|-------------|----------|
| 🌐 **Netlify** | ✅ | `broke-ass-plan.netlify.app` | CDN, Forms, Functions |
| ▲ **Vercel** | ✅ | `broke-ass-plan.vercel.app` | Edge Functions, Analytics |
| 📄 **GitHub Pages** | ✅ | `hugetiny.github.io/broke-ass-plan` | Free, Git-based |
| ☁️ **Cloudflare Pages** | ✅ | `broke-ass-plan.pages.dev` | Global CDN, Workers |
| 🔥 **Firebase Hosting** | ✅ | `broke-ass-plan.web.app` | Google Cloud, Fast |
| 🌊 **Surge.sh** | ✅ | `broke-ass-plan.surge.sh` | Simple, Fast |

### Tier 2: Additional Platforms

| Platform | Deployment Method | Notes |
|----------|------------------|-------|
| 🎨 **Render** | GitHub Actions | Static sites, Auto-deploy |
| 💜 **Heroku** | GitHub Actions | Requires buildpack |
| 🔷 **Azure Static Web Apps** | GitHub Actions | Microsoft Cloud |
| ☁️ **AWS S3 + CloudFront** | GitHub Actions | Enterprise-grade |
| 🌐 **Google Cloud Storage** | GitHub Actions | Google Cloud |
| 🪰 **Fly.io** | GitHub Actions | Global deployment |
| 🦕 **Deno Deploy** | GitHub Actions | Edge runtime |
| ⛈️ **Stormkit** | GitHub Actions | JAMstack platform |
| ⚡ **Fleek** | GitHub Actions | IPFS deployment |

### Tier 3: Manual Setup Required

| Platform | Setup Required |
|----------|----------------|
| **Buddy** | Configure pipeline |
| **Cleavr** | Server setup |
| **Clever Cloud** | App configuration |
| **Azion** | Edge application setup |
| **CloudRay** | Account setup |
| **Flightcontrol** | AWS integration |
| **SST** | AWS CDK setup |
| **Kinsta** | Static site hosting |
| **Zeabur** | Project import |
| **Zerops** | Service configuration |

## 🔧 Configuration Files

The project includes platform-specific configuration files:

- `netlify.toml` - Netlify configuration
- `vercel.json` - Vercel configuration  
- `firebase.json` - Firebase Hosting configuration
- `static.json` - Heroku static buildpack configuration
- `fly.toml` - Fly.io configuration

## 🚀 Manual Deployment Commands

### Netlify
```bash
npm install -g netlify-cli
netlify login
netlify init
netlify deploy --prod
```

### Vercel
```bash
npm install -g vercel
vercel login
vercel --prod
```

### Firebase
```bash
npm install -g firebase-tools
firebase login
firebase init hosting
firebase deploy
```

### Surge.sh
```bash
npm install -g surge
surge dist/ broke-ass-plan.surge.sh
```

### AWS S3
```bash
aws s3 sync dist/ s3://your-bucket-name --delete
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

## 🔒 Security Headers

All deployments include security headers:
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `X-Content-Type-Options: nosniff`
- `Referrer-Policy: strict-origin-when-cross-origin`

## 📊 Monitoring Deployments

- **GitHub Actions**: Check the Actions tab in your repository
- **Platform Dashboards**: Each platform provides deployment logs
- **Status Pages**: Monitor platform status pages for issues

## 🆘 Troubleshooting

### Common Issues:

1. **Build Failures**: Check Node.js version (requires 18+)
2. **Missing Secrets**: Ensure all required secrets are configured
3. **Permission Errors**: Verify API tokens have correct permissions
4. **Domain Issues**: Check DNS settings for custom domains

### Getting Help:

- Check platform-specific documentation
- Review GitHub Actions logs
- Join the [Astro Discord](https://astro.build/chat) for community support

## 🎉 Success!

Once deployed, your site will be available on multiple platforms, providing:
- **High Availability**: Multiple deployment targets
- **Global CDN**: Fast loading worldwide
- **Redundancy**: Backup if one platform has issues
- **Testing**: Compare performance across platforms

Happy deploying! 🚀
