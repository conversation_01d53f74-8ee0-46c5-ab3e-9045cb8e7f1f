# 🚀 官方工具多平台部署方案

使用官方和知名第三方 CLI 工具，实现专业的多平台部署管理。

## 🎯 推荐平台

### 🌟 Tier 1: 强烈推荐

| 平台 | 特点 | 免费额度 | CLI 工具 |
|------|------|----------|----------|
| **Zeabur** | AI 驱动，多区域 | $5/月 | `@zeabur/cli` |
| **Railway** | 官方 Astro 支持 | $5/月 | `@railway/cli` |
| **Vercel** | 边缘函数，性能优秀 | 100GB/月 | `vercel` |

### ⚡ Tier 2: 优秀选择

| 平台 | 特点 | 免费额度 | CLI 工具 |
|------|------|----------|----------|
| **Netlify** | CDN + 函数 | 100GB/月 | `netlify-cli` |
| **Render** | 简单易用 | 100GB/月 | Web 界面 |
| **Surge.sh** | 快速部署 | 无限制 | `surge` |

## 📦 安装 CLI 工具

```bash
# 安装所有推荐的 CLI 工具
npm install -g @zeabur/cli @railway/cli vercel netlify-cli surge

# 或使用 pnpm
pnpm add -g @zeabur/cli @railway/cli vercel netlify-cli surge
```

## 🚀 快速部署

### 使用统一脚本
```powershell
# 查看帮助
.\deploy-official-tools.ps1 help

# 部署到 Zeabur (推荐)
.\deploy-official-tools.ps1 zeabur

# 部署到 Railway
.\deploy-official-tools.ps1 railway

# 部署到 Vercel
.\deploy-official-tools.ps1 vercel
```

### 使用 npm scripts
```bash
# 查看帮助
pnpm run deploy:help

# 部署到各平台
pnpm run deploy:zeabur
pnpm run deploy:railway
pnpm run deploy:vercel
pnpm run deploy:netlify
pnpm run deploy:surge
```

## 🔐 登录各平台

```bash
# Zeabur
zeabur auth login

# Railway
railway login

# Vercel
vercel login

# Netlify
netlify login

# Surge (首次使用时会提示注册)
surge login
```

## 🌟 平台详细对比

### Zeabur 🌟
- ✅ **AI 驱动部署** - 可通过聊天部署
- ✅ **多区域支持** - 全球 6+ 个区域
- ✅ **自动扩缩容** - 按需付费
- ✅ **拖拽部署** - 支持文件夹拖拽
- 💰 **定价**: $5/月免费额度

```bash
# 一键部署
zeabur deploy --name broke-ass-plan

# 指定区域
zeabur deploy --region us-west-1
```

### Railway 🚂
- ✅ **官方 Astro 文档** - 完整的部署指南
- ✅ **GitHub 集成** - 自动部署
- ✅ **实时日志** - 完善的监控
- ✅ **环境变量管理** - 简单配置
- 💰 **定价**: $5/月免费额度

```bash
# 初始化项目
railway init

# 部署
railway up

# 生成域名
railway domain
```

### Vercel ▲
- ✅ **边缘函数** - 全球边缘计算
- ✅ **性能优化** - 自动优化
- ✅ **分析工具** - 详细的性能分析
- ✅ **预览部署** - 分支预览
- 💰 **定价**: 100GB/月免费

```bash
# 一键部署
vercel --prod

# 预览部署
vercel
```

## 🔧 高级配置

### Zeabur 配置 (`zeabur.json`)
```json
{
  "name": "broke-ass-plan",
  "region": "us-west-1",
  "environment": {
    "NODE_VERSION": "18"
  }
}
```

### Railway 配置 (`railway.toml`)
```toml
[build]
builder = "nixpacks"

[deploy]
healthcheckPath = "/"
restartPolicyType = "on_failure"
```

### Vercel 配置 (`vercel.json`)
```json
{
  "buildCommand": "pnpm build",
  "outputDirectory": "dist",
  "framework": "astro"
}
```

## 📊 部署监控

### 查看部署状态
```bash
# Railway
railway status
railway logs

# Vercel
vercel logs
vercel ls

# Netlify
netlify status
netlify logs
```

## 🆘 故障排除

### 常见问题

1. **CLI 工具未找到**:
   ```bash
   # 检查安装
   npm list -g @zeabur/cli
   
   # 重新安装
   npm install -g @zeabur/cli
   ```

2. **登录失败**:
   ```bash
   # 清除缓存重新登录
   zeabur auth logout
   zeabur auth login
   ```

3. **构建失败**:
   ```bash
   # 本地测试构建
   pnpm build
   
   # 检查 Node.js 版本
   node --version
   ```

## 🎉 部署成功后

部署成功后，您的网站将在以下地址可用：

- **Zeabur**: 自动生成的域名
- **Railway**: `your-app.railway.app`
- **Vercel**: `your-app.vercel.app`
- **Netlify**: `your-app.netlify.app`
- **Surge.sh**: `broke-ass-plan.surge.sh`

## 📚 更多资源

- [Zeabur 文档](https://zeabur.com/docs)
- [Railway 文档](https://docs.railway.com/)
- [Vercel 文档](https://vercel.com/docs)
- [Netlify 文档](https://docs.netlify.com/)
- [Astro 部署指南](https://docs.astro.build/en/guides/deploy/)

---

🚀 **开始部署**: 运行 `.\deploy-official-tools.ps1 zeabur` 立即开始！
