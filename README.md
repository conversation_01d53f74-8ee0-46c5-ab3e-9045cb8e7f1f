# 这是一个整理归纳免费白嫖各个云厂商free tier信息的项目。

基本方向是根据github上高星项目，整理归纳免费白嫖各个云厂商free tier信息。

原则：

TODO
1.选择一个框架能够部署到尽量最多的免费平台前端框架，选择官方网站的meta-framework。
官方网站本身就是最好的使用各种免费资源示例
我暂时认为是astro支持的免费一键部署的平台最多。你可以提更好的意见。
最好是基于markdown的，暂时不考虑vue react生态。



## git资源

https://github.com/cloudcommunity/Cloud-Free-Tier-Comparison
Comparing the free tier offers of the major cloud providers like AWS, Azure, GCP, Oracle etc.

https://github.com/255kb/stack-on-a-budget
A collection of services with great free tiers for developers on a budget.

...
## 视频资源

[独立开发者省钱之穷鬼套餐]https://www.bilibili.com/video/BV1kY5XzrE6L
...