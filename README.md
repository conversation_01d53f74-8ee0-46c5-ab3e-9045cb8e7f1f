# 多云免费方案整合平台 | Multi-Cloud Free Solutions Platform

[![Built with Starlight](https://astro.badg.es/v2/built-with-starlight/tiny.svg)](https://starlight.astro.build)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](http://makeapullrequest.com)

> 🌟 **专业的多云免费资源整合平台，帮助开发者和企业构建零成本云架构解决方案**

## 🎯 项目愿景

让每个开发者都能构建企业级的云架构，而无需担心成本问题。通过智能整合各大云厂商的免费资源，我们相信技术创新不应该被预算限制。

## ✨ 核心特性

- 🚀 **零成本启动**: 智能组合各大云厂商免费套餐
- 🔄 **高可用架构**: 多云分布式部署，避免单点故障  
- 📈 **弹性扩展**: 从免费套餐无缝扩展到付费服务
- 🛡️ **风险分散**: 降低厂商锁定风险
- 🌐 **多语言支持**: 中文/英文双语界面
- 📚 **架构模板**: 预设多种常见架构方案

## 🏗️ 项目结构

```
broke-ass-plan/
├── README.md                    # 项目说明
├── astro.config.mjs            # Astro + Starlight 配置
├── package.json                # 项目依赖
├── src/
│   ├── assets/                 # 静态资源
│   ├── content/
│   │   ├── docs/
│   │   │   ├── zh-CN/         # 中文内容 (默认语言)
│   │   │   │   ├── index.mdx  # 中文首页
│   │   │   │   ├── guides/    # 指南文档
│   │   │   │   ├── templates/ # 架构模板
│   │   │   │   ├── providers/ # 云服务商
│   │   │   │   └── categories/# 服务分类
│   │   │   └── en/           # 英文内容
│   │   │       └── index.mdx # 英文首页
│   │   └── content.config.ts # 内容配置
└── public/                    # 公共资源
```

## 🌟 支持的云服务商

- **AWS** - 12个月免费套餐 + 永久免费服务
- **Microsoft Azure** - $200 免费额度 + 12个月免费服务
- **Google Cloud** - $300 免费试用 + 永久免费层级
- **阿里云** - 新用户免费试用 + 免费产品体验
- **腾讯云** - 新用户代金券 + 免费体验
- **Oracle Cloud** - 永久免费的 Always Free 服务
- **Cloudflare** - 永久免费的 CDN 和安全服务
- **Vercel** - 免费的前端部署和边缘函数
- **Netlify** - 免费的静态站点托管
- **Railway** - $5/月免费额度
- **Supabase** - 免费的 PostgreSQL 数据库
- **PlanetScale** - 免费的 MySQL 数据库

## 🚀 快速开始

### 环境要求

- Node.js 18+ 
- pnpm (推荐) 或 npm

### 本地开发

```bash
# 克隆项目
git clone https://github.com/hugetiny/broke-ass-plan.git
cd broke-ass-plan

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev
```

访问 `http://localhost:4321` 查看网站

### 🧞 可用命令

| 命令 | 说明 |
| :--- | :--- |
| `pnpm install` | 安装依赖 |
| `pnpm dev` | 启动开发服务器 (`localhost:4321`) |
| `pnpm build` | 构建生产版本到 `./dist/` |
| `pnpm preview` | 本地预览构建结果 |
| `pnpm astro ...` | 运行 Astro CLI 命令 |

## 🌐 部署

本项目支持部署到多个免费平台：

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/hugetiny/broke-ass-plan)
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/hugetiny/broke-ass-plan)
[![Deploy to GitHub Pages](https://img.shields.io/badge/Deploy%20to-GitHub%20Pages-blue)](https://github.com/hugetiny/broke-ass-plan/actions)

## 🤝 贡献指南

我们欢迎所有形式的贡献！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 贡献内容

- 📝 添加新的云服务商免费套餐信息
- 🏗️ 分享多云架构模板和最佳实践
- 🐛 修复错误和改进文档
- 🌐 翻译和本地化支持
- 💡 提出新功能建议

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Astro](https://astro.build/) - 现代化的静态站点生成器
- [Starlight](https://starlight.astro.build/) - 专业的文档主题
- 所有贡献者和社区成员

---

⭐ 如果这个项目对你有帮助，请给我们一个 Star！
