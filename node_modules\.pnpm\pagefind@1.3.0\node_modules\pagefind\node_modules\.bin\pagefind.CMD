@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\pagefind@1.3.0\node_modules\pagefind\lib\runner\node_modules;C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\pagefind@1.3.0\node_modules\pagefind\lib\node_modules;C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\pagefind@1.3.0\node_modules\pagefind\node_modules;C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\pagefind@1.3.0\node_modules;C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\pagefind@1.3.0\node_modules\pagefind\lib\runner\node_modules;C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\pagefind@1.3.0\node_modules\pagefind\lib\node_modules;C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\pagefind@1.3.0\node_modules\pagefind\node_modules;C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\pagefind@1.3.0\node_modules;C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\lib\runner\bin.cjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\lib\runner\bin.cjs" %*
)
