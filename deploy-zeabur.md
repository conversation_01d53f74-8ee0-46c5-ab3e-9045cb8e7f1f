# 🚀 使用 Zeabur 一键部署到多平台

Zeabur 是一个现代化的部署平台，支持 AI 驱动的部署和多平台管理。

## 📦 安装 Zeabur CLI

```bash
# 使用 npm 安装
npm install -g @zeabur/cli

# 或使用 pnpm
pnpm add -g @zeabur/cli
```

## 🚀 快速部署

### 方法 1: 一键部署 (推荐)
```bash
# 在项目根目录运行
zeabur

# 或者指定项目名称
zeabur deploy --name broke-ass-plan
```

### 方法 2: 从 GitHub 部署
1. 推送代码到 GitHub
2. 访问 [Zeabur Dashboard](https://zeabur.com/projects)
3. 点击 "New Project"
4. 连接 GitHub 仓库
5. 自动检测 Astro 项目并部署

### 方法 3: 拖拽部署
1. 访问 [Zeabur](https://zeabur.com/)
2. 将项目文件夹拖拽到页面
3. 自动分析并部署

## 🌍 多区域部署

Zeabur 支持全球多个区域：
- 🇺🇸 美国 (us-west-1, us-east-1)
- 🇪🇺 欧洲 (eu-west-1)
- 🇯🇵 日本 (ap-northeast-1)
- 🇸🇬 新加坡 (ap-southeast-1)
- 🇭🇰 香港 (ap-east-1)

```bash
# 部署到特定区域
zeabur deploy --region us-west-1
```

## 💰 定价
- **免费套餐**: 每月 $5 免费额度
- **按使用付费**: 只为实际使用的资源付费
- **无需预付费**: 没有固定月费

## 🔧 配置文件 (可选)

创建 `zeabur.json`:
```json
{
  "name": "broke-ass-plan",
  "region": "us-west-1",
  "environment": {
    "NODE_VERSION": "18"
  }
}
```

## 📊 监控和管理
- 实时日志查看
- 性能监控
- 自动扩缩容
- 域名管理
- SSL 证书自动配置
