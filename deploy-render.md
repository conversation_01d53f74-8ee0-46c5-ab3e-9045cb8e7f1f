# 🎨 使用 Render 部署静态站点

Render 提供免费的静态站点托管，支持自动部署。

## 🚀 快速部署

### 方法 1: GitHub 连接 (推荐)
1. 推送代码到 GitHub
2. 访问 [Render Dashboard](https://dashboard.render.com/)
3. 点击 "New Static Site"
4. 连接 GitHub 仓库
5. 配置构建设置：
   - **Build Command**: `pnpm build`
   - **Publish Directory**: `dist`

### 方法 2: 手动上传
1. 本地构建项目：
   ```bash
   pnpm build
   ```
2. 将 `dist` 文件夹拖拽到 Render

## 🔧 构建配置

在 Render 中设置：
- **Environment**: `Node`
- **Build Command**: `pnpm install && pnpm build`
- **Publish Directory**: `dist`
- **Node Version**: `18`

## 🌐 自定义域名

1. 在 Render Dashboard 中进入站点设置
2. 点击 "Custom Domains"
3. 添加您的域名
4. 配置 DNS CNAME 记录

## 💰 定价
- **免费套餐**: 
  - 100GB 带宽/月
  - 自动 SSL 证书
  - 全球 CDN
- **付费套餐**: $7/月起

## 🔧 环境变量

在 Render Dashboard 中设置环境变量：
- `NODE_VERSION`: `18`
- `ASTRO_TELEMETRY_DISABLED`: `1`

## 📊 特性
- ✅ 自动 HTTPS
- ✅ 全球 CDN
- ✅ 自动部署
- ✅ 分支预览
- ✅ 回滚功能
