# 🚀 Windows 部署指南

基于 [Astro 官方文档](https://docs.astro.build/en/guides/deploy/) 的多平台部署方案。

## 🎯 快速开始

### 1. 查看可用平台
```powershell
.\scripts\deploy-all.ps1 -Platform help
```

### 2. 部署到 GitHub Pages (推荐)
```powershell
# 完整部署流程
.\scripts\deploy-all.ps1 -Platform github

# 或者使用简化命令
.\scripts\deploy-all.ps1 -GitHubPagesOnly
```

### 3. 部署到特定平台
```powershell
# Netlify
.\scripts\deploy-all.ps1 -Platform netlify

# Vercel  
.\scripts\deploy-all.ps1 -Platform vercel

# Cloudflare Pages
.\scripts\deploy-all.ps1 -Platform cloudflare

# Firebase Hosting
.\scripts\deploy-all.ps1 -Platform firebase

# Surge.sh
.\scripts\deploy-all.ps1 -Platform surge
```

### 4. 部署到所有平台
```powershell
.\scripts\deploy-all.ps1 -Platform all
```

## 🌐 支持的平台

| 平台 | 状态 | URL 示例 | 特点 |
|------|------|----------|------|
| **GitHub Pages** | ✅ 自动 | `hugetiny.github.io/broke-ass-plan` | 免费、Git 集成 |
| **Netlify** | ⚡ CLI | `broke-ass-plan.netlify.app` | CDN、表单、函数 |
| **Vercel** | ⚡ CLI | `broke-ass-plan.vercel.app` | 边缘函数、分析 |
| **Cloudflare** | ⚡ CLI | `broke-ass-plan.pages.dev` | 全球 CDN |
| **Firebase** | ⚡ CLI | `broke-ass-plan.web.app` | Google 云 |
| **Surge.sh** | ⚡ CLI | `broke-ass-plan.surge.sh` | 简单快速 |
| **Render** | 📋 手动 | 需要连接 GitHub | 自动部署 |
| **Deno Deploy** | ⚡ CLI | 需要配置 | 边缘运行时 |

## 🔧 前置要求

### 必需工具
- **Node.js 18+**
- **pnpm**: `npm install -g pnpm`
- **Git**

### 可选 CLI 工具
```powershell
# Netlify
npm install -g netlify-cli

# Vercel
npm install -g vercel

# Cloudflare
npm install -g wrangler

# Firebase
npm install -g firebase-tools

# Surge.sh
npm install -g surge
```

## 📋 GitHub Pages 设置

### 自动部署 (推荐)
1. **推送代码到 GitHub**:
   ```powershell
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **启用 GitHub Pages**:
   - 进入 GitHub 仓库设置
   - 找到 "Pages" 部分  
   - 选择 "GitHub Actions" 作为源

3. **自动部署**: 每次推送到 `main` 分支都会自动部署

### 手动部署
```powershell
# 使用脚本部署
.\scripts\deploy-all.ps1 -Platform github
```

## 🎨 自定义配置

### 修改网站配置
编辑 `astro.config.mjs`:
```javascript
export default defineConfig({
  site: 'https://hugetiny.github.io',
  base: '/broke-ass-plan',
  // 其他配置...
})
```

### 平台特定配置
- `netlify.toml` - Netlify 配置
- `vercel.json` - Vercel 配置  
- `firebase.json` - Firebase 配置
- `static.json` - Heroku 配置

## 🚨 故障排除

### 常见问题

1. **PowerShell 执行策略错误**:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **Git 推送失败**:
   ```powershell
   # 使用 HTTPS 而不是 SSH
   git remote set-url origin https://github.com/hugetiny/broke-ass-plan.git
   ```

3. **构建失败**:
   ```powershell
   # 清理并重新安装依赖
   Remove-Item -Recurse -Force node_modules
   Remove-Item pnpm-lock.yaml
   pnpm install
   ```

4. **CLI 工具未找到**:
   ```powershell
   # 检查是否已安装
   Get-Command netlify -ErrorAction SilentlyContinue
   
   # 如果未安装
   npm install -g netlify-cli
   ```

### 获取帮助
- 查看脚本帮助: `.\scripts\deploy-all.ps1 -Platform help`
- 检查平台状态页面
- 查看各平台的官方文档

## 🎉 成功部署后

部署成功后，您的网站将在以下地址可用：

- **GitHub Pages**: https://hugetiny.github.io/broke-ass-plan/
- **Netlify**: 检查您的 Netlify 仪表板
- **Vercel**: 检查您的 Vercel 仪表板  
- **Cloudflare**: https://broke-ass-plan.pages.dev/
- **Firebase**: 检查您的 Firebase 控制台
- **Surge.sh**: https://broke-ass-plan.surge.sh/

> 💡 **提示**: 某些平台可能需要几分钟才能更新内容

## 📚 更多资源

- [Astro 官方部署文档](https://docs.astro.build/en/guides/deploy/)
- [GitHub Pages 文档](https://docs.github.com/en/pages)
- [Netlify 文档](https://docs.netlify.com/)
- [Vercel 文档](https://vercel.com/docs)
- [Cloudflare Pages 文档](https://developers.cloudflare.com/pages/)

---

🚀 **准备好了吗？** 运行 `.\scripts\deploy-all.ps1 -Platform github` 开始部署！
