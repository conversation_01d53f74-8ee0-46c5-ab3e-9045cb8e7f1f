hoistPattern:
  - '*'
hoistedDependencies:
  '@astrojs/compiler@2.12.2':
    '@astrojs/compiler': private
  '@astrojs/internal-helpers@0.6.1':
    '@astrojs/internal-helpers': private
  '@astrojs/markdown-remark@6.3.2':
    '@astrojs/markdown-remark': private
  '@astrojs/mdx@4.3.0(astro@5.11.0(@types/node@24.0.10)(rollup@4.44.2)(typescript@5.8.3))':
    '@astrojs/mdx': private
  '@astrojs/prism@3.3.0':
    '@astrojs/prism': private
  '@astrojs/sitemap@3.4.1':
    '@astrojs/sitemap': private
  '@astrojs/telemetry@3.3.0':
    '@astrojs/telemetry': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/types@7.28.0':
    '@babel/types': private
  '@capsizecss/unpack@2.4.0':
    '@capsizecss/unpack': private
  '@ctrl/tinycolor@4.1.0':
    '@ctrl/tinycolor': private
  '@esbuild/aix-ppc64@0.25.6':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.6':
    '@esbuild/win32-x64': private
  '@expressive-code/core@0.41.3':
    '@expressive-code/core': private
  '@expressive-code/plugin-frames@0.41.3':
    '@expressive-code/plugin-frames': private
  '@expressive-code/plugin-shiki@0.41.3':
    '@expressive-code/plugin-shiki': private
  '@expressive-code/plugin-text-markers@0.41.3':
    '@expressive-code/plugin-text-markers': private
  '@img/sharp-darwin-arm64@0.34.2':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.2':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.2':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.2':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.2':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.2':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.2':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.2':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.2':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.2':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.2':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.2':
    '@img/sharp-win32-x64': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@mdx-js/mdx@3.1.0(acorn@8.15.0)':
    '@mdx-js/mdx': private
  '@oslojs/encoding@1.1.0':
    '@oslojs/encoding': private
  '@pagefind/darwin-arm64@1.3.0':
    '@pagefind/darwin-arm64': private
  '@pagefind/darwin-x64@1.3.0':
    '@pagefind/darwin-x64': private
  '@pagefind/default-ui@1.3.0':
    '@pagefind/default-ui': private
  '@pagefind/linux-arm64@1.3.0':
    '@pagefind/linux-arm64': private
  '@pagefind/linux-x64@1.3.0':
    '@pagefind/linux-x64': private
  '@pagefind/windows-x64@1.3.0':
    '@pagefind/windows-x64': private
  '@rollup/pluginutils@5.2.0(rollup@4.44.2)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.44.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.44.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.44.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.44.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.44.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.44.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.44.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.44.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.44.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.44.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.44.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.44.2':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.44.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.44.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.44.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.44.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.44.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.44.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.44.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.44.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@shikijs/core@3.7.0':
    '@shikijs/core': private
  '@shikijs/engine-javascript@3.7.0':
    '@shikijs/engine-javascript': private
  '@shikijs/engine-oniguruma@3.7.0':
    '@shikijs/engine-oniguruma': private
  '@shikijs/langs@3.7.0':
    '@shikijs/langs': private
  '@shikijs/themes@3.7.0':
    '@shikijs/themes': private
  '@shikijs/types@3.7.0':
    '@shikijs/types': private
  '@shikijs/vscode-textmate@10.0.2':
    '@shikijs/vscode-textmate': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/fontkit@2.0.8':
    '@types/fontkit': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/js-yaml@4.0.9':
    '@types/js-yaml': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mdx@2.0.13':
    '@types/mdx': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/nlcst@2.0.3':
    '@types/nlcst': private
  '@types/node@24.0.10':
    '@types/node': private
  '@types/sax@1.2.7':
    '@types/sax': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@6.2.1:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-iterate@2.0.1:
    array-iterate: private
  astring@1.9.0:
    astring: private
  astro-expressive-code@0.41.3(astro@5.11.0(@types/node@24.0.10)(rollup@4.44.2)(typescript@5.8.3)):
    astro-expressive-code: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  base-64@1.0.0:
    base-64: private
  base64-js@1.5.1:
    base64-js: private
  bcp-47-match@2.0.3:
    bcp-47-match: private
  bcp-47@2.1.0:
    bcp-47: private
  blob-to-buffer@1.2.9:
    blob-to-buffer: private
  boolbase@1.0.0:
    boolbase: private
  boxen@8.0.1:
    boxen: private
  brotli@1.3.3:
    brotli: private
  camelcase@8.0.0:
    camelcase: private
  ccount@2.0.1:
    ccount: private
  chalk@5.4.1:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@3.0.0:
    character-entities-legacy: private
  character-entities@2.0.2:
    character-entities: private
  character-reference-invalid@2.0.1:
    character-reference-invalid: private
  chokidar@4.0.3:
    chokidar: private
  ci-info@4.3.0:
    ci-info: private
  cli-boxes@3.0.0:
    cli-boxes: private
  clone@2.1.2:
    clone: private
  clsx@2.1.1:
    clsx: private
  collapse-white-space@2.1.0:
    collapse-white-space: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  common-ancestor-path@1.0.1:
    common-ancestor-path: private
  cookie-es@1.2.2:
    cookie-es: private
  cookie@1.0.2:
    cookie: private
  cross-fetch@3.2.0:
    cross-fetch: private
  crossws@0.3.5:
    crossws: private
  css-selector-parser@3.1.3:
    css-selector-parser: private
  css-tree@3.1.0:
    css-tree: private
  cssesc@3.0.0:
    cssesc: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  defu@6.1.4:
    defu: private
  dequal@2.0.3:
    dequal: private
  destr@2.0.5:
    destr: private
  detect-libc@2.0.4:
    detect-libc: private
  deterministic-object-hash@2.0.2:
    deterministic-object-hash: private
  devalue@5.1.1:
    devalue: private
  devlop@1.1.0:
    devlop: private
  dfa@1.2.0:
    dfa: private
  diff@5.2.0:
    diff: private
  direction@2.0.1:
    direction: private
  dlv@1.1.3:
    dlv: private
  dset@3.1.4:
    dset: private
  emoji-regex@10.4.0:
    emoji-regex: private
  entities@6.0.1:
    entities: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  esast-util-from-estree@2.0.0:
    esast-util-from-estree: private
  esast-util-from-js@2.0.1:
    esast-util-from-js: private
  esbuild@0.25.6:
    esbuild: private
  escape-string-regexp@5.0.0:
    escape-string-regexp: private
  estree-util-attach-comments@3.0.0:
    estree-util-attach-comments: private
  estree-util-build-jsx@3.0.1:
    estree-util-build-jsx: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  estree-util-scope@1.0.0:
    estree-util-scope: private
  estree-util-to-js@2.0.0:
    estree-util-to-js: private
  estree-util-visit@2.0.0:
    estree-util-visit: private
  estree-walker@3.0.3:
    estree-walker: private
  eventemitter3@5.0.1:
    eventemitter3: private
  expressive-code@0.41.3:
    expressive-code: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  flattie@1.1.1:
    flattie: private
  fontace@0.3.0:
    fontace: private
  fontkit@2.0.4:
    fontkit: private
  fsevents@2.3.3:
    fsevents: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  github-slugger@2.0.0:
    github-slugger: private
  h3@1.15.3:
    h3: private
  hast-util-embedded@3.0.0:
    hast-util-embedded: private
  hast-util-format@1.1.0:
    hast-util-format: private
  hast-util-from-html@2.0.3:
    hast-util-from-html: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-has-property@3.0.0:
    hast-util-has-property: private
  hast-util-is-body-ok-link@3.0.1:
    hast-util-is-body-ok-link: private
  hast-util-is-element@3.0.0:
    hast-util-is-element: private
  hast-util-minify-whitespace@1.0.1:
    hast-util-minify-whitespace: private
  hast-util-parse-selector@4.0.0:
    hast-util-parse-selector: private
  hast-util-phrasing@3.0.1:
    hast-util-phrasing: private
  hast-util-raw@9.1.0:
    hast-util-raw: private
  hast-util-select@6.0.4:
    hast-util-select: private
  hast-util-to-estree@3.1.3:
    hast-util-to-estree: private
  hast-util-to-html@9.0.5:
    hast-util-to-html: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-to-parse5@8.0.0:
    hast-util-to-parse5: private
  hast-util-to-string@3.0.1:
    hast-util-to-string: private
  hast-util-to-text@4.0.2:
    hast-util-to-text: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@9.0.1:
    hastscript: private
  html-escaper@3.0.3:
    html-escaper: private
  html-void-elements@3.0.0:
    html-void-elements: private
  html-whitespace-sensitive-tag-names@3.0.1:
    html-whitespace-sensitive-tag-names: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  i18next@23.16.8:
    i18next: private
  import-meta-resolve@4.1.0:
    import-meta-resolve: private
  inline-style-parser@0.2.4:
    inline-style-parser: private
  iron-webcrypto@1.2.1:
    iron-webcrypto: private
  is-alphabetical@2.0.1:
    is-alphabetical: private
  is-alphanumerical@2.0.1:
    is-alphanumerical: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-decimal@2.0.1:
    is-decimal: private
  is-docker@3.0.0:
    is-docker: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-hexadecimal@2.0.1:
    is-hexadecimal: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-wsl@3.1.0:
    is-wsl: private
  js-yaml@4.1.0:
    js-yaml: private
  kleur@4.1.5:
    kleur: private
  klona@2.0.6:
    klona: private
  longest-streak@3.1.0:
    longest-streak: private
  lru-cache@10.4.3:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  magicast@0.3.5:
    magicast: private
  markdown-extensions@2.0.0:
    markdown-extensions: private
  markdown-table@3.0.4:
    markdown-table: private
  mdast-util-definitions@6.0.0:
    mdast-util-definitions: private
  mdast-util-directive@3.1.0:
    mdast-util-directive: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdx@3.0.0:
    mdast-util-mdx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdn-data@2.12.2:
    mdn-data: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-directive@3.0.2:
    micromark-extension-directive: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-extension-mdx-expression@3.0.1:
    micromark-extension-mdx-expression: private
  micromark-extension-mdx-jsx@3.0.2:
    micromark-extension-mdx-jsx: private
  micromark-extension-mdx-md@2.0.0:
    micromark-extension-mdx-md: private
  micromark-extension-mdxjs-esm@3.0.0:
    micromark-extension-mdxjs-esm: private
  micromark-extension-mdxjs@3.0.0:
    micromark-extension-mdxjs: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-mdx-expression@2.0.3:
    micromark-factory-mdx-expression: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-events-to-acorn@2.0.3:
    micromark-util-events-to-acorn: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  neotraverse@0.6.18:
    neotraverse: private
  nlcst-to-string@4.0.0:
    nlcst-to-string: private
  node-fetch-native@1.6.6:
    node-fetch-native: private
  node-fetch@2.7.0:
    node-fetch: private
  node-mock-http@1.0.1:
    node-mock-http: private
  normalize-path@3.0.0:
    normalize-path: private
  nth-check@2.1.1:
    nth-check: private
  ofetch@1.4.1:
    ofetch: private
  ohash@2.0.11:
    ohash: private
  oniguruma-parser@0.12.1:
    oniguruma-parser: private
  oniguruma-to-es@4.3.3:
    oniguruma-to-es: private
  p-limit@6.2.0:
    p-limit: private
  p-queue@8.1.0:
    p-queue: private
  p-timeout@6.1.4:
    p-timeout: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  pagefind@1.3.0:
    pagefind: private
  pako@0.2.9:
    pako: private
  parse-entities@4.0.2:
    parse-entities: private
  parse-latin@7.0.0:
    parse-latin: private
  parse5@7.3.0:
    parse5: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss@8.5.6:
    postcss: private
  prismjs@1.30.0:
    prismjs: private
  prompts@2.4.2:
    prompts: private
  property-information@7.1.0:
    property-information: private
  radix3@1.1.2:
    radix3: private
  readdirp@4.1.2:
    readdirp: private
  recma-build-jsx@1.0.0:
    recma-build-jsx: private
  recma-jsx@1.0.0(acorn@8.15.0):
    recma-jsx: private
  recma-parse@1.0.0:
    recma-parse: private
  recma-stringify@1.0.0:
    recma-stringify: private
  regex-recursion@6.0.2:
    regex-recursion: private
  regex-utilities@2.3.0:
    regex-utilities: private
  regex@6.0.1:
    regex: private
  rehype-expressive-code@0.41.3:
    rehype-expressive-code: private
  rehype-format@5.0.1:
    rehype-format: private
  rehype-parse@9.0.1:
    rehype-parse: private
  rehype-raw@7.0.0:
    rehype-raw: private
  rehype-recma@1.0.0:
    rehype-recma: private
  rehype-stringify@10.0.1:
    rehype-stringify: private
  rehype@13.0.2:
    rehype: private
  remark-directive@3.0.1:
    remark-directive: private
  remark-gfm@4.0.1:
    remark-gfm: private
  remark-mdx@3.1.0:
    remark-mdx: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-smartypants@3.0.2:
    remark-smartypants: private
  remark-stringify@11.0.0:
    remark-stringify: private
  restructure@3.0.2:
    restructure: private
  retext-latin@4.0.0:
    retext-latin: private
  retext-smartypants@6.2.0:
    retext-smartypants: private
  retext-stringify@4.0.0:
    retext-stringify: private
  retext@9.0.0:
    retext: private
  rollup@4.44.2:
    rollup: private
  sax@1.4.1:
    sax: private
  semver@7.7.2:
    semver: private
  shiki@3.7.0:
    shiki: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  sitemap@8.0.0:
    sitemap: private
  smol-toml@1.4.1:
    smol-toml: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.7.4:
    source-map: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  stream-replace-string@2.0.0:
    stream-replace-string: private
  string-width@7.2.0:
    string-width: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@7.1.0:
    strip-ansi: private
  style-to-js@1.1.17:
    style-to-js: private
  style-to-object@1.0.9:
    style-to-object: private
  tiny-inflate@1.0.3:
    tiny-inflate: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tr46@0.0.3:
    tr46: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  tsconfck@3.1.6(typescript@5.8.3):
    tsconfck: private
  tslib@2.8.1:
    tslib: private
  type-fest@4.41.0:
    type-fest: private
  typescript@5.8.3:
    typescript: private
  ufo@1.6.1:
    ufo: private
  ultrahtml@1.6.0:
    ultrahtml: private
  uncrypto@0.1.3:
    uncrypto: private
  undici-types@7.8.0:
    undici-types: private
  unicode-properties@1.4.1:
    unicode-properties: private
  unicode-trie@2.0.0:
    unicode-trie: private
  unified@11.0.5:
    unified: private
  unifont@0.5.2:
    unifont: private
  unist-util-find-after@5.0.0:
    unist-util-find-after: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-modify-children@4.0.0:
    unist-util-modify-children: private
  unist-util-position-from-estree@2.0.0:
    unist-util-position-from-estree: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-remove-position@5.0.0:
    unist-util-remove-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-children@3.0.0:
    unist-util-visit-children: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  unstorage@1.16.0:
    unstorage: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  vite@6.3.5(@types/node@24.0.10):
    vite: private
  vitefu@1.1.1(vite@6.3.5(@types/node@24.0.10)):
    vitefu: private
  web-namespaces@2.0.1:
    web-namespaces: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-pm-runs@1.1.0:
    which-pm-runs: private
  widest-line@5.0.0:
    widest-line: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  xxhash-wasm@1.1.0:
    xxhash-wasm: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yocto-queue@1.2.1:
    yocto-queue: private
  yocto-spinner@0.2.3:
    yocto-spinner: private
  yoctocolors@2.1.1:
    yoctocolors: private
  zod-to-json-schema@3.24.6(zod@3.25.75):
    zod-to-json-schema: private
  zod-to-ts@1.2.0(typescript@5.8.3)(zod@3.25.75):
    zod-to-ts: private
  zod@3.25.75:
    zod: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - sharp
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Tue, 08 Jul 2025 06:22:11 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@emnapi/runtime@1.4.4'
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-arm64@0.25.6'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.25.6'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-arm64@0.34.2'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-darwin-x64@0.34.2'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm64@0.34.2'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-arm@0.34.2'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-s390x@0.34.2'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linux-x64@0.34.2'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.34.2'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.34.2'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-wasm32@0.34.2'
  - '@img/sharp-win32-arm64@0.34.2'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-ia32@0.34.2'
  - '@pagefind/darwin-arm64@1.3.0'
  - '@pagefind/darwin-x64@1.3.0'
  - '@pagefind/linux-arm64@1.3.0'
  - '@pagefind/linux-x64@1.3.0'
  - '@rollup/rollup-android-arm-eabi@4.44.2'
  - '@rollup/rollup-android-arm64@4.44.2'
  - '@rollup/rollup-darwin-arm64@4.44.2'
  - '@rollup/rollup-darwin-x64@4.44.2'
  - '@rollup/rollup-freebsd-arm64@4.44.2'
  - '@rollup/rollup-freebsd-x64@4.44.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.2'
  - '@rollup/rollup-linux-arm64-gnu@4.44.2'
  - '@rollup/rollup-linux-arm64-musl@4.44.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-musl@4.44.2'
  - '@rollup/rollup-linux-s390x-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-musl@4.44.2'
  - '@rollup/rollup-win32-arm64-msvc@4.44.2'
  - '@rollup/rollup-win32-ia32-msvc@4.44.2'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\StudioProjects\broke-ass-plan\node_modules\.pnpm
virtualStoreDirMaxLength: 60
