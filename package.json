{"name": "interstellar-ice", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "deploy": "./scripts/deploy-all.sh", "deploy:netlify": "netlify deploy --prod --dir=dist", "deploy:vercel": "vercel --prod", "deploy:firebase": "firebase deploy", "deploy:surge": "surge dist/ broke-ass-plan.surge.sh"}, "dependencies": {"@astrojs/starlight": "^0.34.4", "astro": "^5.6.1", "sharp": "^0.34.2"}}